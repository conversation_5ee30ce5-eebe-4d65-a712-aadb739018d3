/**
 * 动作评分系统
 * 实现多维度评分算法、难度等级适配和历史数据分析
 */
import { ref, computed } from 'vue'

export function useActionScoring() {
  // 评分历史
  const scoreHistory = ref([])
  
  // 当前评分
  const currentScores = ref({
    overall: 0,
    accuracy: 0,
    stability: 0,
    completeness: 100,
    consistency: 0,
    progressScore: 0  // 新增：渐进式得分
  })

  // 渐进式得分配置
  const PROGRESSIVE_SCORING = {
    smoothingFactor: 0.8,     // 平滑因子，控制分数变化速度（提高响应性）
    minUpdateThreshold: 0.5,  // 最小更新阈值（降低阈值，提高敏感度）
    maxScoreJump: 25,         // 单次最大分数跳跃（允许更大变化）
    decayRate: 0.9           // 分数衰减率（当检测质量下降时）
  }

  // 难度等级权重配置
  const DIFFICULTY_WEIGHTS = {
    easy: {
      accuracy: 0.4,      // 准确性权重
      stability: 0.2,     // 稳定性权重
      completeness: 0.3,  // 完整性权重
      consistency: 0.1,   // 一致性权重
      bonusThreshold: 70, // 奖励分数阈值
      bonusMultiplier: 1.1 // 奖励倍数
    },
    medium: {
      accuracy: 0.5,
      stability: 0.25,
      completeness: 0.2,
      consistency: 0.05,
      bonusThreshold: 80,
      bonusMultiplier: 1.05
    },
    hard: {
      accuracy: 0.6,
      stability: 0.3,
      completeness: 0.1,
      consistency: 0.0,
      bonusThreshold: 85,
      bonusMultiplier: 1.02
    }
  }

  // 稳定性计算配置
  const STABILITY_CONFIG = {
    windowSize: 10,        // 计算窗口大小
    maxVariance: 400,      // 最大方差
    smoothingFactor: 0.3   // 平滑因子
  }

  /**
   * 计算多维度评分
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @param {string} actionType - 动作类型
   * @returns {Object} - 多维度评分结果
   */
  const calculateMultiDimensionalScore = (detectionResult, difficultyLevel = 'easy', actionType) => {
    const weights = DIFFICULTY_WEIGHTS[difficultyLevel]
    
    // 获取基础准确性分数
    const accuracyScore = detectionResult.accuracy || 0

    // 使用渐进式得分（如果检测器提供）
    const rawProgressScore = detectionResult.progressScore !== undefined ?
      detectionResult.progressScore : accuracyScore

    // 应用渐进式平滑 - 但对于新的三阶段检测器，减少平滑以提高响应性
    const smoothedProgressScore = detectionResult.actionStage ?
      applyLightSmoothing(rawProgressScore) : applyProgressiveSmoothing(rawProgressScore)

    // 计算稳定性分数
    const stabilityScore = calculateStabilityScore(smoothedProgressScore, actionType)

    // 计算完整性分数
    const completenessScore = calculateCompletenessScore(detectionResult)

    // 计算一致性分数
    const consistencyScore = calculateConsistencyScore(smoothedProgressScore, actionType)

    // 计算综合分数 - 优先使用渐进式得分
    const rawOverallScore =
      smoothedProgressScore * weights.accuracy +
      stabilityScore * weights.stability +
      completenessScore * weights.completeness +
      consistencyScore * weights.consistency

    // 应用难度奖励
    const overallScore = applyDifficultyBonus(rawOverallScore, weights)

    // 更新当前分数
    const scores = {
      overall: Math.round(Math.min(100, Math.max(0, overallScore))),
      accuracy: Math.round(accuracyScore),
      stability: Math.round(stabilityScore),
      completeness: Math.round(completenessScore),
      consistency: Math.round(consistencyScore),
      progressScore: Math.round(smoothedProgressScore)
    }

    currentScores.value = scores
    
    // 记录到历史
    addToScoreHistory(scores, actionType, detectionResult.stage)
    
    return scores
  }

  /**
   * 应用渐进式平滑算法
   * @param {number} newScore - 新的分数
   * @returns {number} - 平滑后的分数
   */
  const applyProgressiveSmoothing = (newScore) => {
    const currentProgress = currentScores.value.progressScore || 0
    const config = PROGRESSIVE_SCORING

    // 计算分数差异
    const scoreDiff = newScore - currentProgress

    // 如果分数差异小于阈值，不更新
    if (Math.abs(scoreDiff) < config.minUpdateThreshold) {
      return currentProgress
    }

    // 限制单次最大跳跃
    const limitedDiff = Math.sign(scoreDiff) * Math.min(Math.abs(scoreDiff), config.maxScoreJump)

    // 应用平滑因子
    const smoothedScore = currentProgress + (limitedDiff * config.smoothingFactor)

    // 如果新分数较低，应用衰减
    if (newScore < currentProgress) {
      return Math.max(newScore, currentProgress * config.decayRate)
    }

    return Math.min(100, Math.max(0, smoothedScore))
  }

  /**
   * 应用轻量级平滑算法 - 用于三阶段检测器的高响应性
   * @param {number} newScore - 新的分数
   * @returns {number} - 轻度平滑后的分数
   */
  const applyLightSmoothing = (newScore) => {
    const currentProgress = currentScores.value.progressScore || 0

    // 使用更高的响应性参数
    const lightSmoothingFactor = 0.9  // 90%的新分数权重
    const smoothedScore = currentProgress * (1 - lightSmoothingFactor) + newScore * lightSmoothingFactor

    return Math.min(100, Math.max(0, smoothedScore))
  }

  /**
   * 计算稳定性分数
   * @param {number} currentAccuracy - 当前准确性分数
   * @param {string} actionType - 动作类型
   * @returns {number} - 稳定性分数
   */
  const calculateStabilityScore = (currentAccuracy, actionType) => {
    const recentScores = getRecentScores(actionType, STABILITY_CONFIG.windowSize)
    
    if (recentScores.length < 3) {
      return 100 // 初始阶段给予满分
    }
    
    // 计算分数方差
    const mean = recentScores.reduce((sum, score) => sum + score.accuracy, 0) / recentScores.length
    const variance = recentScores.reduce((sum, score) => sum + Math.pow(score.accuracy - mean, 2), 0) / recentScores.length
    
    // 将方差转换为稳定性分数（方差越小，稳定性越高）
    const stabilityScore = Math.max(0, 100 - (variance / STABILITY_CONFIG.maxVariance) * 100)
    
    // 应用平滑因子
    const previousStability = currentScores.value.stability
    return previousStability * (1 - STABILITY_CONFIG.smoothingFactor) + 
           stabilityScore * STABILITY_CONFIG.smoothingFactor
  }

  /**
   * 计算完整性分数
   * @param {Object} detectionResult - 检测结果
   * @returns {number} - 完整性分数
   */
  const calculateCompletenessScore = (detectionResult) => {
    // 检查画面完整性
    if (detectionResult.stage === 'incomplete') {
      return 0
    }
    
    if (detectionResult.stage === 'invalid') {
      return 20
    }
    
    // 基于检测方法调整分数
    if (detectionResult.detectionMethod === 'wrist_fallback' || 
        detectionResult.detectionMethod === 'fallback') {
      return 80 // 使用备用检测方法时给予较低分数
    }
    
    return 100 // 完整检测
  }

  /**
   * 计算一致性分数
   * @param {number} currentAccuracy - 当前准确性分数
   * @param {string} actionType - 动作类型
   * @returns {number} - 一致性分数
   */
  const calculateConsistencyScore = (currentAccuracy, actionType) => {
    const recentScores = getRecentScores(actionType, 5)
    
    if (recentScores.length < 3) {
      return 100
    }
    
    // 计算分数趋势
    const scores = recentScores.map(s => s.accuracy)
    let trendScore = 100
    
    // 检查是否有持续改善的趋势
    let improvementCount = 0
    for (let i = 1; i < scores.length; i++) {
      if (scores[i] >= scores[i - 1]) {
        improvementCount++
      }
    }
    
    // 一致性奖励：如果大部分时间都在改善或保持，给予高分
    const improvementRatio = improvementCount / (scores.length - 1)
    trendScore = improvementRatio * 100
    
    return trendScore
  }

  /**
   * 应用难度奖励
   * @param {number} rawScore - 原始分数
   * @param {Object} weights - 权重配置
   * @returns {number} - 调整后的分数
   */
  const applyDifficultyBonus = (rawScore, weights) => {
    if (rawScore >= weights.bonusThreshold) {
      return rawScore * weights.bonusMultiplier
    }
    return rawScore
  }

  /**
   * 获取最近的分数记录
   * @param {string} actionType - 动作类型
   * @param {number} count - 获取数量
   * @returns {Array} - 最近的分数记录
   */
  const getRecentScores = (actionType, count) => {
    return scoreHistory.value
      .filter(record => record.actionType === actionType)
      .slice(-count)
  }

  /**
   * 添加分数到历史记录
   * @param {Object} scores - 分数对象
   * @param {string} actionType - 动作类型
   * @param {string} stage - 动作阶段
   */
  const addToScoreHistory = (scores, actionType, stage) => {
    const record = {
      ...scores,
      actionType,
      stage,
      timestamp: Date.now()
    }
    
    scoreHistory.value.push(record)
    
    // 保持历史记录在合理范围内
    if (scoreHistory.value.length > 200) {
      scoreHistory.value = scoreHistory.value.slice(-100)
    }
  }

  /**
   * 计算动作完成度
   * @param {Object} scores - 当前分数
   * @param {string} difficultyLevel - 难度等级
   * @returns {number} - 完成度百分比
   */
  const calculateCompletionPercentage = (scores, difficultyLevel = 'easy') => {
    const weights = DIFFICULTY_WEIGHTS[difficultyLevel]
    const threshold = weights.bonusThreshold
    
    return Math.min(100, (scores.overall / threshold) * 100)
  }

  /**
   * 检查是否达到完成条件 - 新的基于检测器状态的判断
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    // 优先使用检测器的完成状态
    if (detectionResult.isCompleted === true) {
      const weights = DIFFICULTY_WEIGHTS[difficultyLevel]
      const minScore = weights.bonusThreshold - 10 // 降低完成要求

      // 检查渐进式得分是否达到最低要求
      const finalScore = detectionResult.progressScore || detectionResult.accuracy || 0
      return finalScore >= minScore
    }

    return false
  }



  /**
   * 获取分数等级
   * @param {number} score - 分数
   * @returns {Object} - 等级信息
   */
  const getScoreGrade = (score) => {
    if (score >= 95) {
      return { grade: 'S', label: '完美', color: 'text-yellow-400' }
    } else if (score >= 90) {
      return { grade: 'A+', label: '优秀', color: 'text-green-400' }
    } else if (score >= 80) {
      return { grade: 'A', label: '良好', color: 'text-green-300' }
    } else if (score >= 70) {
      return { grade: 'B', label: '合格', color: 'text-blue-300' }
    } else if (score >= 60) {
      return { grade: 'C', label: '及格', color: 'text-yellow-300' }
    } else {
      return { grade: 'D', label: '需要改进', color: 'text-red-300' }
    }
  }

  /**
   * 重置评分数据
   * @param {string} actionType - 动作类型（可选，不提供则重置所有）
   */
  const resetScoring = (actionType = null) => {
    if (actionType) {
      scoreHistory.value = scoreHistory.value.filter(record => record.actionType !== actionType)
    } else {
      scoreHistory.value = []
    }
    
    currentScores.value = {
      overall: 0,
      accuracy: 0,
      stability: 0,
      completeness: 100,
      consistency: 0,
      progressScore: 0
    }
  }

  // 计算属性
  const averageScore = computed(() => {
    if (scoreHistory.value.length === 0) return 0
    const sum = scoreHistory.value.reduce((total, record) => total + record.overall, 0)
    return Math.round(sum / scoreHistory.value.length)
  })

  const currentGrade = computed(() => {
    return getScoreGrade(currentScores.value.overall)
  })

  const scoreProgress = computed(() => {
    if (scoreHistory.value.length < 2) return 0
    const recent = scoreHistory.value.slice(-5)
    const older = scoreHistory.value.slice(-10, -5)
    
    if (older.length === 0) return 0
    
    const recentAvg = recent.reduce((sum, r) => sum + r.overall, 0) / recent.length
    const olderAvg = older.reduce((sum, r) => sum + r.overall, 0) / older.length
    
    return recentAvg - olderAvg
  })

  return {
    // 响应式数据
    currentScores,
    scoreHistory,
    
    // 计算属性
    averageScore,
    currentGrade,
    scoreProgress,
    
    // 方法
    calculateMultiDimensionalScore,
    calculateCompletionPercentage,
    isActionCompleted,
    applyProgressiveSmoothing,
    getScoreGrade,
    resetScoring,

    // 配置
    DIFFICULTY_WEIGHTS,
    STABILITY_CONFIG,
    PROGRESSIVE_SCORING
  }
}
