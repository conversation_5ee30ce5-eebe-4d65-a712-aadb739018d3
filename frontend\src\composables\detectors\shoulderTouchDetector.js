/**
 * 肩部触摸动作检测器
 * 实现三阶段检测和渐进式评分逻辑：靠近 → 触摸/峰值 → 归位
 */
import { ref } from 'vue'
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'

export function useShoulderTouchDetector() {
  const engine = useActionDetectionEngine()

  // 动作状态追踪
  const actionState = ref({
    currentStage: 'resting',           // 当前阶段：resting, approaching, peak, returning, completed
    baselineDistance: null,            // 基线距离（起始位置）
    peakDistance: null,                // 峰值距离（最接近时的距离）
    stageStartTime: null,              // 当前阶段开始时间
    progressScore: 0,                  // 渐进式得分
    stageHistory: []                   // 阶段历史记录
  })

  // 动作所需关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_SHOULDER, KeyPointMapping.LEFT_SHOULDER],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.LEFT_SHOULDER, KeyPointMapping.RIGHT_SHOULDER]
  }

  // 难度等级配置 - 调整为更容易完成
  const DIFFICULTY_CONFIG = {
    easy: {
      touchThreshold: 0.35,      // 触摸距离阈值（相对肩宽）- 放宽
      approachThreshold: 0.65,   // 接近距离阈值 - 放宽
      perfectThreshold: 0.25,    // 完美触摸阈值 - 放宽
      returnThreshold: 0.8,      // 归位阈值（相对基线距离）
      minCompletionScore: 60,    // 最低完成得分
      maxScore: 100,             // 最高得分
      stageTimeouts: {           // 各阶段超时时间（秒）
        approaching: 15,
        peak: 10,
        returning: 15
      }
    },
    medium: {
      touchThreshold: 0.28,
      approachThreshold: 0.55,
      perfectThreshold: 0.18,
      returnThreshold: 0.75,
      minCompletionScore: 70,
      maxScore: 100,
      stageTimeouts: {
        approaching: 12,
        peak: 8,
        returning: 12
      }
    },
    hard: {
      touchThreshold: 0.22,
      approachThreshold: 0.45,
      perfectThreshold: 0.15,
      returnThreshold: 0.7,
      minCompletionScore: 80,
      maxScore: 100,
      stageTimeouts: {
        approaching: 10,
        peak: 6,
        returning: 10
      }
    }
  }

  /**
   * 检测肩部触摸动作 - 三阶段渐进式检测
   * @param {Array} keypoints - 关键点数组（画布坐标）
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectShoulderTouch = (keypoints, side = 'left', difficultyLevel = 'easy', canvasWidth = 640, canvasHeight = 480) => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    const requiredPoints = REQUIRED_KEYPOINTS[side]

    // 检查关键点完整性（使用画布坐标）
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side, canvasWidth, canvasHeight, false)
    if (!completenessCheck.isComplete) {
      return createInvalidResult('incomplete', completenessCheck.message)
    }

    // 获取关键点
    const activeWrist = side === 'left' ?
      keypoints[KeyPointMapping.LEFT_WRIST] :
      keypoints[KeyPointMapping.RIGHT_WRIST]

    const targetShoulder = side === 'left' ?
      keypoints[KeyPointMapping.RIGHT_SHOULDER] :
      keypoints[KeyPointMapping.LEFT_SHOULDER]

    // 计算距离（使用画布坐标）
    const distance = engine.calculateDistance(activeWrist, targetShoulder, false)
    const normalizedDistance = engine.normalizeDistance(distance, keypoints, false)

    if (normalizedDistance === Infinity) {
      return createInvalidResult('invalid', '无法计算动作距离')
    }

    // 执行三阶段状态机检测
    const stageResult = processActionStage(normalizedDistance, config, side)

    return {
      ...stageResult,
      distance,
      normalizedDistance,
      actionState: { ...actionState.value }
    }
  }

  /**
   * 创建无效结果对象
   */
  const createInvalidResult = (stage, feedback) => {
    return {
      accuracy: 0,
      stage,
      feedback,
      distance: Infinity,
      normalizedDistance: Infinity,
      progressScore: 0,
      actionStage: actionState.value.currentStage
    }
  }

  /**
   * 处理三阶段动作状态机
   * @param {number} normalizedDistance - 标准化距离
   * @param {Object} config - 难度配置
   * @param {string} side - 动作侧面
   * @returns {Object} - 检测结果
   */
  const processActionStage = (normalizedDistance, config, side) => {
    const currentTime = Date.now()
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    // 初始化基线距离
    if (actionState.value.baselineDistance === null) {
      actionState.value.baselineDistance = normalizedDistance
      actionState.value.currentStage = 'resting'
      actionState.value.stageStartTime = currentTime
    }

    let result = null

    switch (actionState.value.currentStage) {
      case 'resting':
        result = handleRestingStage(normalizedDistance, config, side, currentTime)
        break
      case 'approaching':
        result = handleApproachingStage(normalizedDistance, config, side, currentTime)
        break
      case 'peak':
        result = handlePeakStage(normalizedDistance, config, side, currentTime)
        break
      case 'returning':
        result = handleReturningStage(normalizedDistance, config, side, currentTime)
        break
      case 'completed':
        result = handleCompletedStage(normalizedDistance, config, side)
        break
      default:
        result = resetToResting(normalizedDistance, config, side)
    }

    return result
  }

  /**
   * 处理静息阶段
   */
  const handleRestingStage = (normalizedDistance, config, side, currentTime) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    // 检测是否开始向目标移动（距离明显减少）
    const distanceReduction = actionState.value.baselineDistance - normalizedDistance

    if (distanceReduction > 0.1) { // 距离减少超过阈值，开始接近阶段
      transitionToStage('approaching', currentTime)
      actionState.value.progressScore = 10 // 开始给予基础分数

      return {
        accuracy: 10,
        stage: 'approaching',
        feedback: `很好！开始将${sideText}向${targetText}移动`,
        progressScore: actionState.value.progressScore,
        actionStage: 'approaching'
      }
    }

    return {
      accuracy: 0,
      stage: 'resting',
      feedback: `请将${sideText}向${targetText}移动`,
      progressScore: 0,
      actionStage: 'resting'
    }
  }

  /**
   * 处理接近阶段
   */
  const handleApproachingStage = (normalizedDistance, config, side, currentTime) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    // 检查是否超时
    if (isStageTimeout(currentTime, config.stageTimeouts.approaching)) {
      return resetToResting(normalizedDistance, config, side)
    }

    // 更新峰值距离
    if (actionState.value.peakDistance === null || normalizedDistance < actionState.value.peakDistance) {
      actionState.value.peakDistance = normalizedDistance
    }

    // 检测是否达到触摸阈值或个人峰值
    if (normalizedDistance <= config.touchThreshold) {
      transitionToStage('peak', currentTime)
      actionState.value.progressScore = Math.min(80, actionState.value.progressScore + 30)

      return {
        accuracy: actionState.value.progressScore,
        stage: 'peak',
        feedback: `太棒了！${sideText}触摸到${targetText}了！`,
        progressScore: actionState.value.progressScore,
        actionStage: 'peak'
      }
    }

    // 检测是否开始远离（可能达到个人峰值）
    const isMovingAway = normalizedDistance > actionState.value.peakDistance + 0.05
    if (isMovingAway && actionState.value.peakDistance <= config.approachThreshold) {
      transitionToStage('peak', currentTime)
      actionState.value.progressScore = Math.min(70, actionState.value.progressScore + 20)

      return {
        accuracy: actionState.value.progressScore,
        stage: 'peak',
        feedback: `很好！已经达到您的最佳位置`,
        progressScore: actionState.value.progressScore,
        actionStage: 'peak'
      }
    }

    // 继续接近阶段
    const approachProgress = Math.max(0, (config.approachThreshold - normalizedDistance) / config.approachThreshold)
    actionState.value.progressScore = Math.min(60, 10 + approachProgress * 50)

    return {
      accuracy: actionState.value.progressScore,
      stage: 'approaching',
      feedback: `继续！${sideText}正在接近${targetText}`,
      progressScore: actionState.value.progressScore,
      actionStage: 'approaching'
    }
  }

  /**
   * 处理峰值阶段
   */
  const handlePeakStage = (normalizedDistance, config, side, currentTime) => {
    const sideText = side === 'left' ? '左手' : '右手'

    // 检查是否超时
    if (isStageTimeout(currentTime, config.stageTimeouts.peak)) {
      transitionToStage('returning', currentTime)
    }

    // 检测是否开始归位（距离明显增加）
    const isReturning = normalizedDistance > actionState.value.peakDistance + 0.1
    if (isReturning) {
      transitionToStage('returning', currentTime)
      actionState.value.progressScore = Math.min(90, actionState.value.progressScore + 10)

      return {
        accuracy: actionState.value.progressScore,
        stage: 'returning',
        feedback: `很好！现在慢慢将${sideText}放回原位`,
        progressScore: actionState.value.progressScore,
        actionStage: 'returning'
      }
    }

    // 保持峰值阶段
    return {
      accuracy: actionState.value.progressScore,
      stage: 'peak',
      feedback: `保持这个姿势，然后慢慢放回`,
      progressScore: actionState.value.progressScore,
      actionStage: 'peak'
    }
  }

  /**
   * 处理归位阶段
   */
  const handleReturningStage = (normalizedDistance, config, side, currentTime) => {
    const sideText = side === 'left' ? '左手' : '右手'

    // 检查是否超时
    if (isStageTimeout(currentTime, config.stageTimeouts.returning)) {
      return resetToResting(normalizedDistance, config, side)
    }

    // 检查是否归位到基线附近
    const returnProgress = normalizedDistance / actionState.value.baselineDistance
    if (returnProgress >= config.returnThreshold) {
      // 动作完成
      transitionToStage('completed', currentTime)
      actionState.value.progressScore = Math.min(100, actionState.value.progressScore + 10)

      return {
        accuracy: actionState.value.progressScore,
        stage: 'completed',
        feedback: `完成！${sideText}动作做得很好！`,
        progressScore: actionState.value.progressScore,
        actionStage: 'completed',
        isCompleted: true
      }
    }

    // 继续归位过程
    const returnScore = Math.min(95, actionState.value.progressScore + (returnProgress * 5))
    actionState.value.progressScore = returnScore

    return {
      accuracy: returnScore,
      stage: 'returning',
      feedback: `继续将${sideText}慢慢放回原位`,
      progressScore: returnScore,
      actionStage: 'returning'
    }
  }

  /**
   * 处理完成阶段
   */
  const handleCompletedStage = () => {
    return {
      accuracy: actionState.value.progressScore,
      stage: 'completed',
      feedback: '动作已完成！准备下一个动作',
      progressScore: actionState.value.progressScore,
      actionStage: 'completed',
      isCompleted: true
    }
  }

  /**
   * 阶段转换函数
   */
  const transitionToStage = (newStage, currentTime) => {
    actionState.value.stageHistory.push({
      stage: actionState.value.currentStage,
      duration: currentTime - actionState.value.stageStartTime,
      timestamp: currentTime
    })

    actionState.value.currentStage = newStage
    actionState.value.stageStartTime = currentTime

    console.log(`[ShoulderTouchDetector] 阶段转换: ${actionState.value.stageHistory[actionState.value.stageHistory.length - 1]?.stage} -> ${newStage}`)
  }

  /**
   * 检查阶段是否超时
   */
  const isStageTimeout = (currentTime, timeoutSeconds) => {
    const elapsedTime = (currentTime - actionState.value.stageStartTime) / 1000
    return elapsedTime > timeoutSeconds
  }

  /**
   * 重置到静息状态
   */
  const resetToResting = (normalizedDistance, config, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    actionState.value.currentStage = 'resting'
    actionState.value.baselineDistance = normalizedDistance
    actionState.value.peakDistance = null
    actionState.value.progressScore = 0
    actionState.value.stageStartTime = Date.now()

    return {
      accuracy: 0,
      stage: 'resting',
      feedback: `请重新开始，将${sideText}向${targetText}移动`,
      progressScore: 0,
      actionStage: 'resting'
    }
  }

  /**
   * 重置动作状态
   */
  const resetActionState = () => {
    actionState.value = {
      currentStage: 'resting',
      baselineDistance: null,
      peakDistance: null,
      stageStartTime: null,
      progressScore: 0,
      stageHistory: []
    }
  }

  /**
   * 检查动作是否完成 - 新的完成逻辑
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    return detectionResult.isCompleted === true &&
           detectionResult.progressScore >= config.minCompletionScore
  }

  /**
   * 获取动作指导建议
   * @param {Object} detectionResult - 检测结果
   * @param {string} side - 动作侧面
   * @returns {string} - 指导建议
   */
  const getGuidance = (detectionResult, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    switch (detectionResult.stage) {
      case 'incomplete':
        return detectionResult.feedback
      case 'too_far':
        return `抬起${sideText}，慢慢向${targetText}移动`
      case 'preparing':
        return `继续将${sideText}向${targetText}靠近`
      case 'approaching':
        return `很好！${sideText}快要触摸到${targetText}了`
      case 'good_touch':
        return `保持${sideText}触摸${targetText}的姿势`
      case 'perfect_touch':
        return `完美！保持这个姿势`
      default:
        return '请按照示范动作进行'
    }
  }

  return {
    // 主要检测方法
    detectShoulderTouch,
    isActionCompleted,
    getGuidance,

    // 状态管理
    actionState,
    resetActionState,

    // 配置
    REQUIRED_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
